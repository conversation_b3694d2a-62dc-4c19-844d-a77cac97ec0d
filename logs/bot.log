2025-08-02 08:28:59,081 - __main__ - WARNING - Slow command execution: watchlist took 6.51s
2025-08-02 08:33:33,782 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=8f4c8e4f149015e70e5d621ed55a8e8c524ee1a6fa907af223baa313699a5177
2025-08-02 08:33:33,783 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=8f4c8e4f149015e70e5d621ed55a8e8c524ee1a6fa907af223baa313699a5177 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=8f4c8e4f149015e70e5d621ed55a8e8c524ee1a6fa907af223baa313699a5177)
2025-08-02 08:35:40,871 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=0122fd940bea2288bbfb802fd5c67f663c1890e5a92e03468fdab226ee867629
2025-08-02 08:35:40,871 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=0122fd940bea2288bbfb802fd5c67f663c1890e5a92e03468fdab226ee867629 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=0122fd940bea2288bbfb802fd5c67f663c1890e5a92e03468fdab226ee867629)
2025-08-02 08:39:53,022 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=9b2dca208a5deb28894c3f78fdfc9c502cc8a46b99d980aba0dbf3a1de3ea9e3
2025-08-02 08:39:53,023 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=9b2dca208a5deb28894c3f78fdfc9c502cc8a46b99d980aba0dbf3a1de3ea9e3 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=9b2dca208a5deb28894c3f78fdfc9c502cc8a46b99d980aba0dbf3a1de3ea9e3)
2025-08-02 09:24:46,728 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=e1e7f6da446683420c4203b24374195a4796cecb3f59624724b9b8eeab24f748
2025-08-02 09:24:46,728 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=e1e7f6da446683420c4203b24374195a4796cecb3f59624724b9b8eeab24f748 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=e1e7f6da446683420c4203b24374195a4796cecb3f59624724b9b8eeab24f748)
2025-08-02 13:45:21,625 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 4 messages from #paxg (keep_pinned: True)
2025-08-02 14:04:32,973 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 43.3s behind.
2025-08-02 14:59:46,188 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=e341bae24f7650254df0ff64ef07619b8262bdcafd41e1803ffb48c89c0d3515
2025-08-02 14:59:46,189 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=e341bae24f7650254df0ff64ef07619b8262bdcafd41e1803ffb48c89c0d3515 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=e341bae24f7650254df0ff64ef07619b8262bdcafd41e1803ffb48c89c0d3515)
2025-08-02 15:07:16,442 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=bb242c4793b6cbc51878053dafed22078edebff2a02bdd20eb9e1ec9171eb9a2
2025-08-02 15:07:16,442 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=bb242c4793b6cbc51878053dafed22078edebff2a02bdd20eb9e1ec9171eb9a2 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=bb242c4793b6cbc51878053dafed22078edebff2a02bdd20eb9e1ec9171eb9a2)
2025-08-02 15:25:01,384 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=ecbc0e1546c9737f6ba1b3b69d2e242710568707b8ea4fdd9250a3f00e92c84c
2025-08-02 15:25:01,384 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=ecbc0e1546c9737f6ba1b3b69d2e242710568707b8ea4fdd9250a3f00e92c84c (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=ecbc0e1546c9737f6ba1b3b69d2e242710568707b8ea4fdd9250a3f00e92c84c)
2025-08-02 15:33:24,706 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=ac80cbfb03d1d987473509118cf1826c92aa6e802fb45777878a7d5e6a87cb12
2025-08-02 15:33:24,707 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=ac80cbfb03d1d987473509118cf1826c92aa6e802fb45777878a7d5e6a87cb12 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=ac80cbfb03d1d987473509118cf1826c92aa6e802fb45777878a7d5e6a87cb12)
2025-08-03 00:55:32,711 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 43.5s behind.
2025-08-03 12:06:11,086 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102595533213726/messages/1397130631867138151 responded with 429. Retrying in 0.41 seconds.
2025-08-03 12:06:12,117 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102595533213726/messages/1397130157403275276 responded with 429. Retrying in 0.38 seconds.
2025-08-03 12:06:13,117 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102595533213726/messages/1397126948593078333 responded with 429. Retrying in 0.38 seconds.
2025-08-03 12:06:14,087 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102595533213726/messages/1396410540909396062 responded with 429. Retrying in 0.41 seconds.
2025-08-03 12:06:15,210 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102595533213726/messages/1396404877017743360 responded with 429. Retrying in 0.30 seconds.
2025-08-03 12:06:16,160 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102595533213726/messages/1396404346916175892 responded with 429. Retrying in 0.33 seconds.
2025-08-03 12:06:17,150 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102595533213726/messages/1396404192993611856 responded with 429. Retrying in 0.35 seconds.
2025-08-03 12:06:18,314 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 14 messages from #📝-bot-logs (keep_pinned: True)
2025-08-03 12:07:33,141 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1396021939482525747 responded with 429. Retrying in 0.52 seconds.
2025-08-03 12:07:34,315 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1394625205544030270 responded with 429. Retrying in 0.34 seconds.
2025-08-03 12:07:35,225 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1394550764101173349 responded with 429. Retrying in 0.43 seconds.
2025-08-03 12:07:36,284 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1394348243890081862 responded with 429. Retrying in 0.37 seconds.
2025-08-03 12:07:37,310 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1394314501326901280 responded with 429. Retrying in 0.35 seconds.
2025-08-03 12:07:38,278 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1394306129437266013 responded with 429. Retrying in 0.38 seconds.
2025-08-03 12:07:39,221 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1394299896911233075 responded with 429. Retrying in 0.43 seconds.
2025-08-03 12:07:40,229 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1394294687808098334 responded with 429. Retrying in 0.42 seconds.
2025-08-03 12:07:41,264 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1394294643931222138 responded with 429. Retrying in 0.39 seconds.
2025-08-03 12:07:42,319 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1394294628097724496 responded with 429. Retrying in 0.34 seconds.
2025-08-03 12:07:43,279 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1394291607804579902 responded with 429. Retrying in 0.38 seconds.
2025-08-03 12:07:44,273 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1394283267292921856 responded with 429. Retrying in 0.38 seconds.
2025-08-03 12:07:45,384 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1394283046282334258 responded with 429. Retrying in 0.30 seconds.
2025-08-03 12:07:46,373 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 20 messages from #💬-general (keep_pinned: True)
2025-08-03 12:15:24,798 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102516969967636/messages/1383429691075199101 responded with 429. Retrying in 0.55 seconds.
2025-08-03 12:15:26,002 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102516969967636/messages/1379819461779128454 responded with 429. Retrying in 0.35 seconds.
2025-08-03 12:15:27,001 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102516969967636/messages/1379818401748357172 responded with 429. Retrying in 0.35 seconds.
2025-08-03 12:15:28,076 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102516969967636/messages/1379812969747316757 responded with 429. Retrying in 0.30 seconds.
2025-08-03 12:15:29,928 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 11 messages from #📢-announcements (keep_pinned: True)
2025-08-03 12:15:53,752 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1390961458141069384/messages/1390961806830469191 responded with 429. Retrying in 0.45 seconds.
2025-08-03 12:15:54,874 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1390961458141069384/messages/1390961717479080029 responded with 429. Retrying in 0.33 seconds.
2025-08-03 12:15:56,063 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 9 messages from #note (keep_pinned: True)
2025-08-03 13:13:44,169 - __main__ - WARNING - Slow command execution: watchlist took 6.66s
2025-08-03 19:59:41,962 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=f86c0fcff622b7b79c69c10760505a9d0298540dbca137e0aa0f16c72ded5fd5
2025-08-03 19:59:41,962 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=f86c0fcff622b7b79c69c10760505a9d0298540dbca137e0aa0f16c72ded5fd5 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=f86c0fcff622b7b79c69c10760505a9d0298540dbca137e0aa0f16c72ded5fd5)
2025-08-04 00:20:46,628 - services.market.market_service - ERROR - Error fetching OHLCV data for BNBUSDT: binance GET https://fapi.binance.com/fapi/v1/klines?interval=1d&limit=21&symbol=BNBUSDT
2025-08-04 03:42:02,080 - discord.client - ERROR - Attempting a reconnect in 0.50s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-04 04:36:24,116 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 3 messages from #trade (keep_pinned: True)
2025-08-04 04:36:34,806 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 4 messages from #📝-bot-logs (keep_pinned: True)
2025-08-04 04:46:59,250 - __main__ - WARNING - Slow command execution: watchlist took 6.56s
2025-08-04 06:40:39,772 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 556, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/trading/data_service.py", line 64, in _update_loop
    await self.update_all_data()
  File "/root/chartfix/services/trading/data_service.py", line 128, in update_all_data
    orders_result = self.trading_service.get_open_orders()
  File "/root/chartfix/services/core/error_service.py", line 83, in wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/trading/trading_service.py", line 335, in get_open_orders
    orders = self.exchange.fetch_open_orders(formatted_symbol)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 6739, in fetch_open_orders
    response = self.fapiPrivateGetOpenOrders(self.extend(request, params))
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/adapters.py", line 486, in send
    resp = conn.urlopen(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 700, in urlopen
    httplib_response = self._make_request(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 441, in _make_request
    httplib_response = conn.getresponse()
  File "/usr/lib/python3.10/http/client.py", line 1375, in getresponse
    response.begin()
  File "/usr/lib/python3.10/http/client.py", line 318, in begin
    version, status, reason = self._read_status()
  File "/usr/lib/python3.10/http/client.py", line 279, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
  File "/usr/lib/python3.10/socket.py", line 705, in readinto
    return self._sock.recv_into(b)
  File "/usr/lib/python3.10/ssl.py", line 1303, in recv_into
    return self.read(nbytes, buffer)
  File "/usr/lib/python3.10/ssl.py", line 1159, in read
    return self._sslobj.read(len, buffer)

2025-08-04 06:40:40,463 - services.trading.trading_service - ERROR - Get orders error: binance {"code":-1021,"msg":"Timestamp for this request is outside of the recvWindow."}
2025-08-04 06:44:12,593 - services.trading.trading_service - ERROR - Network error fetching balance: binance {"code":-1021,"msg":"Timestamp for this request is outside of the recvWindow."}
2025-08-04 06:44:12,594 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance {"code":-1021,"msg":"Timestamp for this request is outside of the recvWindow."} (Caused by: InvalidNonce: binance {"code":-1021,"msg":"Timestamp for this request is outside of the recvWindow."})
2025-08-04 06:44:17,497 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 556, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/trading/data_service.py", line 64, in _update_loop
    await self.update_all_data()
  File "/root/chartfix/services/trading/data_service.py", line 128, in update_all_data
    orders_result = self.trading_service.get_open_orders()
  File "/root/chartfix/services/core/error_service.py", line 83, in wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/trading/trading_service.py", line 335, in get_open_orders
    orders = self.exchange.fetch_open_orders(formatted_symbol)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 6739, in fetch_open_orders
    response = self.fapiPrivateGetOpenOrders(self.extend(request, params))
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4374, in fetch2
    self.throttle(cost)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 463, in throttle
    time.sleep(delay / 1000.0)

2025-08-04 07:34:26,691 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 556, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/trading/data_service.py", line 64, in _update_loop
    await self.update_all_data()
  File "/root/chartfix/services/trading/data_service.py", line 80, in update_all_data
    account_result = self.trading_service.get_account_balance()
  File "/root/chartfix/services/core/error_service.py", line 83, in wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/core/error_service.py", line 171, in sync_wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/trading/trading_service.py", line 67, in get_account_balance
    account_info = self.exchange.fapiPrivateV3GetAccount()
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/adapters.py", line 486, in send
    resp = conn.urlopen(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 700, in urlopen
    httplib_response = self._make_request(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 441, in _make_request
    httplib_response = conn.getresponse()
  File "/usr/lib/python3.10/http/client.py", line 1375, in getresponse
    response.begin()
  File "/usr/lib/python3.10/http/client.py", line 318, in begin
    version, status, reason = self._read_status()
  File "/usr/lib/python3.10/http/client.py", line 279, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
  File "/usr/lib/python3.10/socket.py", line 705, in readinto
    return self._sock.recv_into(b)
  File "/usr/lib/python3.10/ssl.py", line 1303, in recv_into
    return self.read(nbytes, buffer)
  File "/usr/lib/python3.10/ssl.py", line 1159, in read
    return self._sslobj.read(len, buffer)

2025-08-04 07:34:33,797 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=a59339a3c61f4f5433794e4d5437b31e8efb709024f246cfb8b87c4c4507fb16
2025-08-04 07:34:33,798 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=a59339a3c61f4f5433794e4d5437b31e8efb709024f246cfb8b87c4c4507fb16 (Caused by: RequestTimeout: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=a59339a3c61f4f5433794e4d5437b31e8efb709024f246cfb8b87c4c4507fb16)
2025-08-04 07:34:36,693 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 556, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/trading/data_service.py", line 64, in _update_loop
    await self.update_all_data()
  File "/root/chartfix/services/trading/data_service.py", line 128, in update_all_data
    orders_result = self.trading_service.get_open_orders()
  File "/root/chartfix/services/core/error_service.py", line 83, in wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/trading/trading_service.py", line 335, in get_open_orders
    orders = self.exchange.fetch_open_orders(formatted_symbol)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 6739, in fetch_open_orders
    response = self.fapiPrivateGetOpenOrders(self.extend(request, params))
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4374, in fetch2
    self.throttle(cost)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 463, in throttle
    time.sleep(delay / 1000.0)

2025-08-04 09:21:15,545 - discord.client - ERROR - Attempting a reconnect in 1.87s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-04 10:06:19,910 - discord.client - ERROR - Attempting a reconnect in 0.83s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-04 11:30:45,937 - discord.client - ERROR - Attempting a reconnect in 0.12s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-04 12:05:00,504 - services.trading.trading_service - ERROR - Network error fetching balance: binance {"code":-1021,"msg":"Timestamp for this request is outside of the recvWindow."}
2025-08-04 12:05:00,671 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance {"code":-1021,"msg":"Timestamp for this request is outside of the recvWindow."} (Caused by: InvalidNonce: binance {"code":-1021,"msg":"Timestamp for this request is outside of the recvWindow."})
2025-08-04 12:11:24,838 - discord.gateway - WARNING - Shard ID None has stopped responding to the gateway. Closing and restarting.
2025-08-04 12:42:03,043 - __main__ - WARNING - Slow command execution: watchlist took 8.48s
2025-08-04 12:43:05,120 - asyncio - ERROR - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x7f298d07c1c0>, 1898541.*********)]']
connector: <aiohttp.connector.TCPConnector object at 0x7f29a07ab220>
2025-08-04 12:52:46,328 - asyncio - ERROR - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x7f4678d67dc0>, 1899123.*********)]']
connector: <aiohttp.connector.TCPConnector object at 0x7f468ce76c20>
2025-08-04 12:57:21,235 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 5 messages from #📝-bot-logs (keep_pinned: True)
2025-08-04 12:57:38,633 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 3 messages from #💬-general (keep_pinned: True)
2025-08-04 12:58:12,005 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 3 messages from #trade (keep_pinned: True)
2025-08-04 12:58:50,550 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1397335672439705601 responded with 429. Retrying in 0.53 seconds.
2025-08-04 12:58:51,711 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1397331869292298277 responded with 429. Retrying in 0.37 seconds.
2025-08-04 12:58:52,761 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1397329335559520397 responded with 429. Retrying in 0.34 seconds.
2025-08-04 12:58:53,747 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1397305218781417544 responded with 429. Retrying in 0.34 seconds.
2025-08-04 12:58:54,707 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1397290928007413812 responded with 429. Retrying in 0.37 seconds.
2025-08-04 12:58:55,810 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1397246845553479881 responded with 429. Retrying in 0.30 seconds.
2025-08-04 12:58:56,743 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1397244239385202819 responded with 429. Retrying in 0.34 seconds.
2025-08-04 12:58:57,713 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1397242941537517741 responded with 429. Retrying in 0.37 seconds.
2025-08-04 12:58:58,715 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1397235203650621612 responded with 429. Retrying in 0.37 seconds.
2025-08-04 12:58:59,722 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1397230484815089865 responded with 429. Retrying in 0.37 seconds.
2025-08-04 12:59:00,723 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1397228760033398901 responded with 429. Retrying in 0.36 seconds.
2025-08-04 12:59:01,875 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1397227948045631640 responded with 429. Retrying in 0.30 seconds.
2025-08-04 12:59:02,888 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1397223572425936926 responded with 429. Retrying in 0.30 seconds.
2025-08-04 12:59:03,833 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1397218340304584866 responded with 429. Retrying in 0.30 seconds.
2025-08-04 12:59:04,727 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1397201331826987118 responded with 429. Retrying in 0.35 seconds.
2025-08-04 12:59:09,777 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1397133532547252294 responded with 429. Retrying in 0.32 seconds.
2025-08-04 12:59:10,739 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1397132770400141342 responded with 429. Retrying in 0.34 seconds.
2025-08-04 12:59:11,674 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1397132769007501332 responded with 429. Retrying in 0.41 seconds.
2025-08-04 12:59:12,684 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1397132517387141300 responded with 429. Retrying in 0.40 seconds.
2025-08-04 12:59:13,764 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1397132249954123821 responded with 429. Retrying in 0.32 seconds.
2025-08-04 12:59:14,680 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1397132092529442918 responded with 429. Retrying in 0.40 seconds.
2025-08-04 12:59:15,791 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1397131732712554628 responded with 429. Retrying in 0.30 seconds.
2025-08-04 12:59:16,780 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1397131731504730113 responded with 429. Retrying in 0.30 seconds.
2025-08-04 12:59:17,706 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1397079893493153992 responded with 429. Retrying in 0.39 seconds.
2025-08-04 12:59:18,745 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1397079892130140240 responded with 429. Retrying in 0.34 seconds.
2025-08-04 12:59:19,810 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1397060062517657641 responded with 429. Retrying in 0.30 seconds.
2025-08-04 12:59:20,686 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1397052232817573978 responded with 429. Retrying in 0.39 seconds.
2025-08-04 12:59:21,670 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1397041790472814675 responded with 429. Retrying in 0.41 seconds.
2025-08-04 12:59:22,715 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1397039338931359856 responded with 429. Retrying in 0.37 seconds.
2025-08-04 12:59:23,708 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396974308185604116 responded with 429. Retrying in 0.40 seconds.
2025-08-04 12:59:24,718 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396958735263596766 responded with 429. Retrying in 0.37 seconds.
2025-08-04 12:59:25,725 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396944283252166858 responded with 429. Retrying in 0.36 seconds.
2025-08-04 12:59:26,734 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396940624879419503 responded with 429. Retrying in 0.34 seconds.
2025-08-04 12:59:27,691 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396937937563684864 responded with 429. Retrying in 0.39 seconds.
2025-08-04 12:59:28,777 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396935404325703883 responded with 429. Retrying in 0.30 seconds.
2025-08-04 12:59:29,725 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396923654490554459 responded with 429. Retrying in 0.36 seconds.
2025-08-04 12:59:30,719 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396920193447497950 responded with 429. Retrying in 0.36 seconds.
2025-08-04 12:59:31,652 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396918438290788382 responded with 429. Retrying in 0.43 seconds.
2025-08-04 12:59:32,678 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396889759288266954 responded with 429. Retrying in 0.40 seconds.
2025-08-04 12:59:33,700 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396882155262709852 responded with 429. Retrying in 0.40 seconds.
2025-08-04 12:59:36,706 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396877055853068461 responded with 429. Retrying in 0.37 seconds.
2025-08-04 12:59:37,738 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396873182798942331 responded with 429. Retrying in 0.35 seconds.
2025-08-04 12:59:38,664 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396870753156206674 responded with 429. Retrying in 0.42 seconds.
2025-08-04 12:59:39,675 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396866174800625806 responded with 429. Retrying in 0.41 seconds.
2025-08-04 12:59:40,684 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396862767448719414 responded with 429. Retrying in 0.40 seconds.
2025-08-04 12:59:41,781 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396861887144263712 responded with 429. Retrying in 0.30 seconds.
2025-08-04 12:59:42,719 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396861886061871294 responded with 429. Retrying in 0.36 seconds.
2025-08-04 12:59:43,759 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396859350240329809 responded with 429. Retrying in 0.32 seconds.
2025-08-04 12:59:44,689 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396855971409825802 responded with 429. Retrying in 0.39 seconds.
2025-08-04 12:59:45,767 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396855539345920040 responded with 429. Retrying in 0.33 seconds.
2025-08-04 12:59:46,768 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396854270690726009 responded with 429. Retrying in 0.32 seconds.
2025-08-04 12:59:47,794 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396853793747898580 responded with 429. Retrying in 0.30 seconds.
2025-08-04 12:59:48,791 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396849926075584625 responded with 429. Retrying in 0.30 seconds.
2025-08-04 12:59:49,758 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396849190046273536 responded with 429. Retrying in 0.33 seconds.
2025-08-04 12:59:50,701 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396846653012705322 responded with 429. Retrying in 0.38 seconds.
2025-08-04 12:59:51,677 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396846651670401034 responded with 429. Retrying in 0.41 seconds.
2025-08-04 12:59:52,681 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396846157472337971 responded with 429. Retrying in 0.40 seconds.
2025-08-04 12:59:53,687 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396846155995807887 responded with 429. Retrying in 0.39 seconds.
2025-08-04 12:59:54,712 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396817468038582373 responded with 429. Retrying in 0.37 seconds.
2025-08-04 12:59:55,653 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396760468915486740 responded with 429. Retrying in 0.43 seconds.
2025-08-04 12:59:56,721 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396739715541106698 responded with 429. Retrying in 0.37 seconds.
2025-08-04 12:59:57,712 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396617230900527224 responded with 429. Retrying in 0.37 seconds.
2025-08-04 12:59:58,717 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396617229528862901 responded with 429. Retrying in 0.36 seconds.
2025-08-04 12:59:59,674 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396617228207521842 responded with 429. Retrying in 0.41 seconds.
2025-08-04 13:00:00,824 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396617226131476572 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:00:01,933 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396608109207752875 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:00:02,884 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396581737558970543 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:00:03,986 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396579649882034277 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:00:04,980 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396579203184332811 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:00:06,629 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396571594255503473 responded with 429. Retrying in 0.46 seconds.
2025-08-04 13:00:07,849 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396570579846561843 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:00:12,555 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396549532283703296 responded with 429. Retrying in 0.53 seconds.
2025-08-04 13:00:13,983 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396546240019435530 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:00:14,991 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396539903164026911 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:00:15,990 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396531033247191093 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:00:16,910 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396530954998120569 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:00:17,842 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396529764960964619 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:00:18,813 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396527222449246238 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:00:19,748 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396525687162142841 responded with 429. Retrying in 0.33 seconds.
2025-08-04 13:00:20,702 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396524682190258226 responded with 429. Retrying in 0.38 seconds.
2025-08-04 13:00:21,722 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396520878740602891 responded with 429. Retrying in 0.36 seconds.
2025-08-04 13:00:22,669 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396518892972802152 responded with 429. Retrying in 0.41 seconds.
2025-08-04 13:00:24,451 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396518341325230121 responded with 429. Retrying in 0.63 seconds.
2025-08-04 13:00:25,756 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396515854383845387 responded with 429. Retrying in 0.34 seconds.
2025-08-04 13:00:26,785 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396514538639331431 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:00:27,774 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396514537066332191 responded with 429. Retrying in 0.31 seconds.
2025-08-04 13:00:28,701 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396513427475660800 responded with 429. Retrying in 0.39 seconds.
2025-08-04 13:00:30,079 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396512001575882902 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:00:31,555 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396495521610338476 responded with 429. Retrying in 0.54 seconds.
2025-08-04 13:00:32,705 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396495519995396107 responded with 429. Retrying in 0.38 seconds.
2025-08-04 13:00:33,756 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396493960775598090 responded with 429. Retrying in 0.33 seconds.
2025-08-04 13:00:34,694 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396460129582317639 responded with 429. Retrying in 0.39 seconds.
2025-08-04 13:00:35,803 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396458853293494434 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:00:36,773 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396458745034309754 responded with 429. Retrying in 0.31 seconds.
2025-08-04 13:00:37,771 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396458743394336768 responded with 429. Retrying in 0.31 seconds.
2025-08-04 13:00:38,745 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396457474973765662 responded with 429. Retrying in 0.35 seconds.
2025-08-04 13:00:39,696 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396457473304432796 responded with 429. Retrying in 0.39 seconds.
2025-08-04 13:00:40,674 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396457471236898971 responded with 429. Retrying in 0.41 seconds.
2025-08-04 13:00:41,751 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396453946670846055 responded with 429. Retrying in 0.34 seconds.
2025-08-04 13:00:42,783 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396445774937722910 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:00:43,749 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396442254725611560 responded with 429. Retrying in 0.33 seconds.
2025-08-04 13:00:44,761 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396432110264254585 responded with 429. Retrying in 0.32 seconds.
2025-08-04 13:00:45,955 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396429574325272686 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:00:47,813 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396427023588790282 responded with 429. Retrying in 0.39 seconds.
2025-08-04 13:00:48,817 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396426768210198531 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:00:49,781 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396424126247342210 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:00:50,873 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396413082833784943 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:00:51,863 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396411669147811902 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:00:52,808 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396411307372318801 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:00:53,716 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396370082212352070 responded with 429. Retrying in 0.37 seconds.
2025-08-04 13:00:54,860 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396349799938134087 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:00:55,791 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396332056128000140 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:00:56,890 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396329514258272327 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:00:58,043 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396328245435175116 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:00:59,051 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396313028492005568 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:01:00,023 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396312858744062074 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:01:01,030 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396306692572184687 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:01:01,956 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396301622975397918 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:01:02,918 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396301467857457222 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:01:03,877 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396297821400989776 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:01:04,897 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396267392882970785 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:01:05,889 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396252192050249890 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:01:06,875 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396252016887464147 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:01:07,785 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396241533556101144 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:01:08,689 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1396215431789809788 responded with 429. Retrying in 0.40 seconds.
2025-08-04 13:01:15,828 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1395917611899813968 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:01:16,826 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1395916272931180778 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:01:17,875 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1395878832736698368 responded with 429. Retrying in 0.34 seconds.
2025-08-04 13:01:18,977 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1395860643382558852 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:01:19,967 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1395842561003753612 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:01:20,931 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1395821700062646483 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:01:22,051 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1395821314157056071 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:01:23,007 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1395808662626238588 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:01:24,014 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1395808106377642177 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:01:25,024 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1395807371657216162 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:01:26,032 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1395806695623622741 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:01:27,006 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1395793842220826624 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:01:27,949 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1395793805265076384 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:01:28,846 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1395792500936937562 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:01:29,849 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1395787274490482924 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:01:30,837 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1395783359568937051 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:01:31,883 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1395777374510911629 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:01:32,897 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1395775528228753488 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:01:33,878 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1395774221811978310 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:01:34,759 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1395772917354205326 responded with 429. Retrying in 0.32 seconds.
2025-08-04 13:01:35,688 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1395766467936518254 responded with 429. Retrying in 0.39 seconds.
2025-08-04 13:01:36,684 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1395765176862638223 responded with 429. Retrying in 0.40 seconds.
2025-08-04 13:01:37,918 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1395762173048918178 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:01:38,949 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1395761524265451542 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:01:39,862 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1395758127994896516 responded with 429. Retrying in 0.30 seconds.
2025-08-04 13:01:40,763 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1395754830659584142 responded with 429. Retrying in 0.32 seconds.
2025-08-04 13:01:41,731 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1395754731384868916 responded with 429. Retrying in 0.35 seconds.
2025-08-04 13:01:42,738 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1395753598385651803 responded with 429. Retrying in 0.34 seconds.
2025-08-04 13:01:43,710 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1395750758091329578 responded with 429. Retrying in 0.37 seconds.
2025-08-04 13:01:44,718 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1395749489578610738 responded with 429. Retrying in 0.36 seconds.
2025-08-04 13:01:45,775 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376101651164827740/messages/1395749069443305525 responded with 429. Retrying in 0.32 seconds.
2025-08-04 13:01:46,840 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 583 messages from #🚨-alerts (keep_pinned: True)
2025-08-04 13:32:46,919 - __main__ - WARNING - Slow command execution: watchlist took 7.33s
2025-08-04 13:35:12,151 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 7 messages from #paxg (keep_pinned: False)
2025-08-04 15:29:59,145 - discord.client - ERROR - Attempting a reconnect in 0.90s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-04 17:06:17,712 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 13.7s behind.
2025-08-04 17:06:17,713 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 13.7s behind.
2025-08-05 01:06:09,943 - services.market.short_term_price_alert_service - ERROR - Error fetching OHLCV data for BTCUSDT 1h: binance GET https://fapi.binance.com/fapi/v1/klines?interval=1h&limit=2&symbol=BTCUSDT
2025-08-05 05:11:21,354 - discord.client - ERROR - Attempting a reconnect in 0.43s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-05 06:29:16,329 - discord.client - ERROR - Attempting a reconnect in 0.18s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-05 10:09:50,378 - discord.client - ERROR - Attempting a reconnect in 1.31s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-05 17:39:23,988 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 43.0s behind.
2025-08-06 00:16:48,964 - discord.client - ERROR - Attempting a reconnect in 0.04s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-06 08:34:39,283 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 44.0s behind.
2025-08-06 09:09:54,885 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 43.8s behind.
2025-08-06 09:15:12,535 - discord.client - ERROR - Attempting a reconnect in 0.75s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-06 09:19:31,443 - discord.client - ERROR - Attempting a reconnect in 1.30s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-06 10:42:27,599 - discord.client - ERROR - Attempting a reconnect in 1.85s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-06 11:07:52,121 - discord.client - ERROR - Attempting a reconnect in 1.68s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-06 12:00:09,656 - services.trading.trading_service - ERROR - Get orders error: binance {"code":-1000,"msg":"An unknown error occured while processing the request."}
2025-08-06 12:58:32,562 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 7 messages from #lam-viec (keep_pinned: True)
2025-08-06 13:15:54,773 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=5cc08508fc2aca8074a14055c2d1278e2fd38889491fcf82dd496c70edf0cb7e
2025-08-06 13:15:54,774 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=5cc08508fc2aca8074a14055c2d1278e2fd38889491fcf82dd496c70edf0cb7e (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=5cc08508fc2aca8074a14055c2d1278e2fd38889491fcf82dd496c70edf0cb7e)
2025-08-06 18:03:31,373 - discord.client - ERROR - Attempting a reconnect in 0.19s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-06 18:08:36,740 - discord.client - ERROR - Attempting a reconnect in 2.32s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-06 18:29:44,712 - discord.client - ERROR - Attempting a reconnect in 6.23s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-06 19:46:01,597 - discord.client - ERROR - Attempting a reconnect in 1.98s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-07 00:56:33,358 - discord.client - ERROR - Attempting a reconnect in 1.17s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-07 09:00:02,511 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 484, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/market/short_term_price_alert_service.py", line 284, in start_monitoring
    await self.check_price_movements()
  File "/root/chartfix/services/market/short_term_price_alert_service.py", line 216, in check_price_movements
    price_data = await self.calculate_price_change(symbol, timeframe)
  File "/root/chartfix/services/market/short_term_price_alert_service.py", line 145, in calculate_price_change
    ohlcv_data = await self.fetch_ohlcv_data(symbol, timeframe, limit=2)
  File "/root/chartfix/services/market/short_term_price_alert_service.py", line 118, in fetch_ohlcv_data
    ohlcv = self.exchange.fetch_ohlcv(symbol, exchange_timeframe, limit=limit)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 4501, in fetch_ohlcv
    response = self.fapiPublicGetKlines(self.extend(request, params))
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/adapters.py", line 486, in send
    resp = conn.urlopen(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 700, in urlopen
    httplib_response = self._make_request(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 441, in _make_request
    httplib_response = conn.getresponse()
  File "/usr/lib/python3.10/http/client.py", line 1375, in getresponse
    response.begin()
  File "/usr/lib/python3.10/http/client.py", line 318, in begin
    version, status, reason = self._read_status()
  File "/usr/lib/python3.10/http/client.py", line 279, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
  File "/usr/lib/python3.10/socket.py", line 705, in readinto
    return self._sock.recv_into(b)
  File "/usr/lib/python3.10/ssl.py", line 1303, in recv_into
    return self.read(nbytes, buffer)
  File "/usr/lib/python3.10/ssl.py", line 1159, in read
    return self._sslobj.read(len, buffer)

2025-08-07 09:00:12,517 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 484, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/market/short_term_price_alert_service.py", line 284, in start_monitoring
    await self.check_price_movements()
  File "/root/chartfix/services/market/short_term_price_alert_service.py", line 216, in check_price_movements
    price_data = await self.calculate_price_change(symbol, timeframe)
  File "/root/chartfix/services/market/short_term_price_alert_service.py", line 145, in calculate_price_change
    ohlcv_data = await self.fetch_ohlcv_data(symbol, timeframe, limit=2)
  File "/root/chartfix/services/market/short_term_price_alert_service.py", line 118, in fetch_ohlcv_data
    ohlcv = self.exchange.fetch_ohlcv(symbol, exchange_timeframe, limit=limit)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 4501, in fetch_ohlcv
    response = self.fapiPublicGetKlines(self.extend(request, params))
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/adapters.py", line 486, in send
    resp = conn.urlopen(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 700, in urlopen
    httplib_response = self._make_request(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 441, in _make_request
    httplib_response = conn.getresponse()
  File "/usr/lib/python3.10/http/client.py", line 1375, in getresponse
    response.begin()
  File "/usr/lib/python3.10/http/client.py", line 318, in begin
    version, status, reason = self._read_status()
  File "/usr/lib/python3.10/http/client.py", line 279, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
  File "/usr/lib/python3.10/socket.py", line 705, in readinto
    return self._sock.recv_into(b)
  File "/usr/lib/python3.10/ssl.py", line 1303, in recv_into
    return self.read(nbytes, buffer)
  File "/usr/lib/python3.10/ssl.py", line 1159, in read
    return self._sslobj.read(len, buffer)

2025-08-07 09:00:16,440 - discord.client - ERROR - Attempting a reconnect in 1.33s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-07 12:42:34,860 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=bf23c10df5c10072b6ab1b01af2f4c8459a51d7efa0e0c851d6ff7a67c7fb4e6
2025-08-07 12:42:34,861 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=bf23c10df5c10072b6ab1b01af2f4c8459a51d7efa0e0c851d6ff7a67c7fb4e6 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=bf23c10df5c10072b6ab1b01af2f4c8459a51d7efa0e0c851d6ff7a67c7fb4e6)
2025-08-07 17:03:39,119 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 43.0s behind.
2025-08-07 19:34:06,171 - discord.client - ERROR - Attempting a reconnect in 0.11s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-07 21:05:25,061 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 44.0s behind.
2025-08-07 22:30:13,728 - discord.client - ERROR - Attempting a reconnect in 1.78s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-07 23:23:27,834 - discord.client - ERROR - Attempting a reconnect in 0.78s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-08 00:27:25,868 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=f441945c98e134139e96f7e882ceba926671333580091703da9eb2eb77833bd9
2025-08-08 00:27:25,868 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=f441945c98e134139e96f7e882ceba926671333580091703da9eb2eb77833bd9 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=f441945c98e134139e96f7e882ceba926671333580091703da9eb2eb77833bd9)
2025-08-08 01:53:08,897 - discord.client - ERROR - Attempting a reconnect in 0.43s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-08 05:21:23,328 - discord.client - ERROR - Attempting a reconnect in 1.96s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-08 08:37:48,414 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=6b99dd0c92da11a6f63237d8cb01223d67714b4680236917386342863e466c99
2025-08-08 08:37:48,415 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=6b99dd0c92da11a6f63237d8cb01223d67714b4680236917386342863e466c99 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=6b99dd0c92da11a6f63237d8cb01223d67714b4680236917386342863e466c99)
2025-08-08 08:44:05,560 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=35fa9e10aa3f87c04db330e76bd800a2c8d1a073d08c5a2e34755dcf4307b744
2025-08-08 08:44:05,561 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=35fa9e10aa3f87c04db330e76bd800a2c8d1a073d08c5a2e34755dcf4307b744 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=35fa9e10aa3f87c04db330e76bd800a2c8d1a073d08c5a2e34755dcf4307b744)
2025-08-08 10:20:00,441 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=4d2b6f1519b1cb7c6793273856499c2bb31197a686732695f59f873a0fc30462
2025-08-08 10:20:00,441 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=4d2b6f1519b1cb7c6793273856499c2bb31197a686732695f59f873a0fc30462 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=4d2b6f1519b1cb7c6793273856499c2bb31197a686732695f59f873a0fc30462)
2025-08-08 13:19:41,047 - services.market.market_service - WARNING - CoinGecko: Coin ID 'btc' not found (404)
2025-08-08 13:35:34,296 - discord.client - ERROR - Attempting a reconnect in 1.48s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-08 14:54:09,811 - services.market.market_service - ERROR - Error fetching OHLCV data for BTCUSDT: binance GET https://fapi.binance.com/fapi/v1/klines?interval=4h&limit=21&symbol=BTCUSDT
2025-08-09 00:36:51,136 - services.market.market_service - ERROR - Error fetching Fear & Greed Index: Connection timeout to host https://api.alternative.me/fng/
2025-08-09 03:24:59,810 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=0615738b935fd2a0ea6a089b1c004f24b0c0d110da74198a416aaee28db71079
2025-08-09 03:24:59,810 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=0615738b935fd2a0ea6a089b1c004f24b0c0d110da74198a416aaee28db71079 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=0615738b935fd2a0ea6a089b1c004f24b0c0d110da74198a416aaee28db71079)
2025-08-09 03:30:14,344 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=2048dd9b2d5df22b4a6f60ad1ea8dd0bafb4e350ff562c89e8d1e9f7a906e4f4
2025-08-09 03:30:14,345 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=2048dd9b2d5df22b4a6f60ad1ea8dd0bafb4e350ff562c89e8d1e9f7a906e4f4 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=2048dd9b2d5df22b4a6f60ad1ea8dd0bafb4e350ff562c89e8d1e9f7a906e4f4)
2025-08-09 05:36:20,082 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=a2500faae3a45a179b4decd41737610bd5ecbe2e49be5368d86d1af06193b7cf
2025-08-09 05:36:20,083 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=a2500faae3a45a179b4decd41737610bd5ecbe2e49be5368d86d1af06193b7cf (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=a2500faae3a45a179b4decd41737610bd5ecbe2e49be5368d86d1af06193b7cf)
2025-08-09 06:00:20,045 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=8079f4e0028696cd82d80029d87c68ea99486d455acc707930d84ae6757c39d0
2025-08-09 06:00:20,045 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=8079f4e0028696cd82d80029d87c68ea99486d455acc707930d84ae6757c39d0 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=8079f4e0028696cd82d80029d87c68ea99486d455acc707930d84ae6757c39d0)
2025-08-09 15:45:52,493 - discord.client - ERROR - Attempting a reconnect in 1.46s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-10 01:48:55,165 - discord.client - ERROR - Attempting a reconnect in 1.97s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-10 06:58:14,385 - discord.client - ERROR - Attempting a reconnect in 0.42s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-10 15:54:19,405 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=87ca973f40c40550139f60040dbf0f9048c1559795a06e2cbc3db8f9ff3410fa
2025-08-10 15:54:19,406 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=87ca973f40c40550139f60040dbf0f9048c1559795a06e2cbc3db8f9ff3410fa (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=87ca973f40c40550139f60040dbf0f9048c1559795a06e2cbc3db8f9ff3410fa)
2025-08-10 16:17:17,035 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=82786fe87c4355372772d3bfc0523ebc0daa1a1a1bbca6e1e0b31435f19e546d
2025-08-10 16:17:17,035 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=82786fe87c4355372772d3bfc0523ebc0daa1a1a1bbca6e1e0b31435f19e546d (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=82786fe87c4355372772d3bfc0523ebc0daa1a1a1bbca6e1e0b31435f19e546d)
2025-08-11 07:46:30,601 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=459a0b1ba829c03f068ff16e98c7492762d3c6b680c4cf04d6dfaaf54c0531fc
2025-08-11 07:46:30,602 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=459a0b1ba829c03f068ff16e98c7492762d3c6b680c4cf04d6dfaaf54c0531fc (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=459a0b1ba829c03f068ff16e98c7492762d3c6b680c4cf04d6dfaaf54c0531fc)
2025-08-11 12:39:34,700 - services.market.market_service - ERROR - [MARKET-SERVICE] CCXT error fetching prices for ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'SOLUSDT', 'NEARUSDT', 'LINKUSDT', 'ENAUSDT', 'DOGEUSDT', 'XRPUSDT', 'WLDUSDT']: binance GET https://fapi.binance.com/fapi/v1/ticker/24hr
2025-08-11 12:39:34,701 - services.market.market_service - WARNING - No price data received for watchlist symbols
2025-08-11 15:28:45,231 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 44.5s behind.
2025-08-11 15:33:53,142 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=e85f875873f3b0796f8994e4eff1e9065d9413977a828cfb0fbfdff8035a4af4
2025-08-11 15:33:53,143 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=e85f875873f3b0796f8994e4eff1e9065d9413977a828cfb0fbfdff8035a4af4 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=e85f875873f3b0796f8994e4eff1e9065d9413977a828cfb0fbfdff8035a4af4)
2025-08-11 16:40:49,211 - discord.client - ERROR - Attempting a reconnect in 0.97s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-11 17:38:53,325 - discord.client - ERROR - Attempting a reconnect in 1.66s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-11 18:05:35,595 - discord.client - ERROR - Attempting a reconnect in 2.51s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-11 20:50:00,431 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=7de88355c40f99d1ef5b9b541a17889dbc2299f4bb332b0bd4828e9b4aa7439d
2025-08-11 20:50:00,432 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=7de88355c40f99d1ef5b9b541a17889dbc2299f4bb332b0bd4828e9b4aa7439d (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=7de88355c40f99d1ef5b9b541a17889dbc2299f4bb332b0bd4828e9b4aa7439d)
2025-08-11 22:07:21,868 - discord.client - ERROR - Attempting a reconnect in 1.32s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-11 22:12:33,586 - services.market.market_service - ERROR - [MARKET-SERVICE] CCXT error fetching prices for ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'SOLUSDT', 'NEARUSDT', 'LINKUSDT', 'ENAUSDT', 'DOGEUSDT', 'XRPUSDT', 'WLDUSDT']: binance GET https://fapi.binance.com/fapi/v1/ticker/24hr
2025-08-11 22:12:33,587 - services.market.market_service - WARNING - No price data received for watchlist symbols
2025-08-11 22:31:40,196 - services.market.short_term_price_alert_service - ERROR - Error fetching OHLCV data for BTCUSDT 1h: binance GET https://fapi.binance.com/fapi/v1/klines?interval=1h&limit=2&symbol=BTCUSDT
2025-08-11 23:03:31,483 - discord.client - ERROR - Attempting a reconnect in 0.49s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-12 04:26:50,904 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 484, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/trading/data_service.py", line 64, in _update_loop
    await self.update_all_data()
  File "/root/chartfix/services/trading/data_service.py", line 128, in update_all_data
    orders_result = self.trading_service.get_open_orders()
  File "/root/chartfix/services/core/error_service.py", line 83, in wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/trading/trading_service.py", line 335, in get_open_orders
    orders = self.exchange.fetch_open_orders(formatted_symbol)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 6739, in fetch_open_orders
    response = self.fapiPrivateGetOpenOrders(self.extend(request, params))
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 747, in send
    r.content
  File "/usr/local/lib/python3.10/dist-packages/requests/models.py", line 899, in content
    self._content = b"".join(self.iter_content(CONTENT_CHUNK_SIZE)) or b""
  File "/usr/local/lib/python3.10/dist-packages/requests/models.py", line 816, in generate
    yield from self.raw.stream(chunk_size, decode_content=True)
  File "/usr/lib/python3/dist-packages/urllib3/response.py", line 572, in stream
    for line in self.read_chunked(amt, decode_content=decode_content):
  File "/usr/lib/python3/dist-packages/urllib3/response.py", line 764, in read_chunked
    self._update_chunk_length()
  File "/usr/lib/python3/dist-packages/urllib3/response.py", line 694, in _update_chunk_length
    line = self._fp.fp.readline()
  File "/usr/lib/python3.10/socket.py", line 705, in readinto
    return self._sock.recv_into(b)
  File "/usr/lib/python3.10/ssl.py", line 1303, in recv_into
    return self.read(nbytes, buffer)
  File "/usr/lib/python3.10/ssl.py", line 1159, in read
    return self._sslobj.read(len, buffer)

2025-08-12 04:26:55,715 - discord.client - ERROR - Attempting a reconnect in 0.43s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-12 05:44:19,708 - discord.client - ERROR - Attempting a reconnect in 0.45s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-12 06:55:21,769 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 44.2s behind.
2025-08-12 07:33:43,900 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=bcb5e5a11b419e9b8c20b9ff4e8d079a9588ac1e537db11d9ebb8651ff177fbb
2025-08-12 07:33:43,900 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=bcb5e5a11b419e9b8c20b9ff4e8d079a9588ac1e537db11d9ebb8651ff177fbb (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=bcb5e5a11b419e9b8c20b9ff4e8d079a9588ac1e537db11d9ebb8651ff177fbb)
2025-08-12 07:55:13,334 - discord.client - ERROR - Attempting a reconnect in 0.71s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-12 08:08:23,127 - services.market.market_service - ERROR - Error fetching Fear & Greed Index: Connection timeout to host https://api.alternative.me/fng/
2025-08-12 09:54:16,265 - discord.client - ERROR - Attempting a reconnect in 1.05s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-12 10:57:00,395 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 44.3s behind.
2025-08-12 12:38:01,149 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=39951a355853c07bc8f505e7c5ad33a0877a9e26f0461e81e10a028a74c539fb
2025-08-12 12:38:01,150 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=39951a355853c07bc8f505e7c5ad33a0877a9e26f0461e81e10a028a74c539fb (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=39951a355853c07bc8f505e7c5ad33a0877a9e26f0461e81e10a028a74c539fb)
2025-08-12 13:37:28,565 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=64ccc35c41ca6133e728fe45ab08127a71a4ab52522500c0f15b43b803b7b343
2025-08-12 13:37:28,566 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=64ccc35c41ca6133e728fe45ab08127a71a4ab52522500c0f15b43b803b7b343 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=64ccc35c41ca6133e728fe45ab08127a71a4ab52522500c0f15b43b803b7b343)
2025-08-12 22:20:07,782 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=7500dbcaef434965b4fa38b200869adcea9e50b8eaa936ed848ca415cf23d6d4
2025-08-12 22:20:07,782 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=7500dbcaef434965b4fa38b200869adcea9e50b8eaa936ed848ca415cf23d6d4 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=7500dbcaef434965b4fa38b200869adcea9e50b8eaa936ed848ca415cf23d6d4)
2025-08-13 01:13:22,330 - discord.client - ERROR - Attempting a reconnect in 1.73s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-13 02:35:48,934 - discord.client - ERROR - Attempting a reconnect in 0.74s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-13 05:36:33,608 - services.market.market_service - ERROR - [MARKET-SERVICE] CCXT error fetching prices for ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'SOLUSDT', 'NEARUSDT', 'LINKUSDT', 'ENAUSDT', 'DOGEUSDT', 'XRPUSDT', 'WLDUSDT']: binance GET https://fapi.binance.com/fapi/v1/ticker/24hr
2025-08-13 05:36:33,609 - services.market.market_service - WARNING - No price data received for watchlist symbols
2025-08-13 09:05:19,384 - discord.client - ERROR - Attempting a reconnect in 1.05s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-13 10:51:24,317 - discord.client - ERROR - Attempting a reconnect in 0.18s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-13 14:54:10,490 - services.market.short_term_price_alert_service - ERROR - Error fetching OHLCV data for BTCUSDT 1h: binance GET https://fapi.binance.com/fapi/v1/klines?interval=1h&limit=2&symbol=BTCUSDT
2025-08-13 16:00:09,272 - services.trading.trading_service - ERROR - Get positions error: binance {"code":-1007,"msg":"Timeout waiting for response from backend server. Send status unknown; execution status unknown."}
2025-08-13 16:04:40,084 - services.market.short_term_price_alert_service - ERROR - Error fetching OHLCV data for BTCUSDT 1h: binance GET https://fapi.binance.com/fapi/v1/klines?interval=1h&limit=2&symbol=BTCUSDT
2025-08-13 17:37:49,994 - discord.client - ERROR - Attempting a reconnect in 0.31s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-13 18:11:31,422 - discord.client - ERROR - Attempting a reconnect in 2.96s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-13 18:34:07,723 - discord.client - ERROR - Attempting a reconnect in 6.04s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-13 18:41:19,443 - discord.client - ERROR - Attempting a reconnect in 2.06s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-13 18:46:49,114 - discord.client - ERROR - Attempting a reconnect in 21.97s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-14 10:17:07,506 - discord.client - ERROR - Attempting a reconnect in 0.98s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-14 16:59:09,943 - discord.client - ERROR - Attempting a reconnect in 1.56s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-14 17:12:21,246 - discord.client - ERROR - Attempting a reconnect in 0.54s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-14 17:29:01,165 - discord.client - ERROR - Attempting a reconnect in 2.07s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-14 18:03:04,345 - discord.client - ERROR - Attempting a reconnect in 14.91s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-14 18:10:10,621 - discord.client - ERROR - Attempting a reconnect in 19.89s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-14 19:23:00,404 - discord.client - ERROR - Attempting a reconnect in 1.57s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-14 19:31:55,087 - discord.client - ERROR - Attempting a reconnect in 0.01s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-14 22:34:19,415 - discord.client - ERROR - Attempting a reconnect in 0.73s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-15 03:12:35,246 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=f511b438b24fd3ec7650a81cc373fa4f74a77626e271fb9598577933616ee784
2025-08-15 03:12:35,246 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=f511b438b24fd3ec7650a81cc373fa4f74a77626e271fb9598577933616ee784 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=f511b438b24fd3ec7650a81cc373fa4f74a77626e271fb9598577933616ee784)
2025-08-15 03:55:19,842 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=af9a7cd76f307feabb793c4d535d3b37222ded25e55022b3a2d930a109864297
2025-08-15 03:55:19,843 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=af9a7cd76f307feabb793c4d535d3b37222ded25e55022b3a2d930a109864297 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=af9a7cd76f307feabb793c4d535d3b37222ded25e55022b3a2d930a109864297)
2025-08-15 04:46:39,785 - services.market.market_service - ERROR - Error fetching OHLCV data for BTCUSDT: binance GET https://fapi.binance.com/fapi/v1/klines?interval=4h&limit=21&symbol=BTCUSDT
2025-08-15 07:15:25,185 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=76bbd3bbf7e4ce8f5cec1c53a0ef7c6c3c1fca4531987a5a8518fe4b4cdcdc79
2025-08-15 07:15:25,185 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=76bbd3bbf7e4ce8f5cec1c53a0ef7c6c3c1fca4531987a5a8518fe4b4cdcdc79 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=76bbd3bbf7e4ce8f5cec1c53a0ef7c6c3c1fca4531987a5a8518fe4b4cdcdc79)
2025-08-15 11:17:26,809 - discord.client - ERROR - Attempting a reconnect in 0.78s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-15 23:53:23,861 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 44.0s behind.
2025-08-16 00:28:40,045 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 44.0s behind.
2025-08-16 08:04:50,817 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=e38705d1eeeaccdc6a016b99305f0eeeff820fedcf8e74d5bb4d3a8981743814
2025-08-16 08:04:50,818 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=e38705d1eeeaccdc6a016b99305f0eeeff820fedcf8e74d5bb4d3a8981743814 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=e38705d1eeeaccdc6a016b99305f0eeeff820fedcf8e74d5bb4d3a8981743814)
2025-08-16 13:30:16,976 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=40ab9ad478268d8be013e6fab4f50d3c5500e3a65fe6a1bd8b7e116c88a37e8a
2025-08-16 13:30:16,976 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=40ab9ad478268d8be013e6fab4f50d3c5500e3a65fe6a1bd8b7e116c88a37e8a (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=40ab9ad478268d8be013e6fab4f50d3c5500e3a65fe6a1bd8b7e116c88a37e8a)
2025-08-16 13:38:39,464 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=db7fc0b36322999884120fcbd1878e82c73054da9fc60a817cc809a64acf22c1
2025-08-16 13:38:39,465 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=db7fc0b36322999884120fcbd1878e82c73054da9fc60a817cc809a64acf22c1 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=db7fc0b36322999884120fcbd1878e82c73054da9fc60a817cc809a64acf22c1)
2025-08-16 13:42:10,546 - handlers.discord.trading.advanced_commands - WARNING - Discord HTTP error (attempt 1/3): 503 Service Unavailable (error code: 0): upstream connect error or disconnect/reset before headers. reset reason: remote connection failure, transport failure reason: immediate connect error: Connection refused
2025-08-17 07:21:17,035 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 44.1s behind.
2025-08-17 11:59:23,434 - services.trading.trading_service - ERROR - Get positions error: binance GET https://fapi.binance.com/fapi/v3/positionRisk?timestamp=*************&recvWindow=10000&signature=abbd8605ba935faf86fac1d5b2f36328e1c0420ff7c058b7c7392ed3a1461d71
2025-08-17 18:49:40,154 - services.market.market_service - ERROR - Error fetching OHLCV data for BTCUSDT: binance GET https://fapi.binance.com/fapi/v1/klines?interval=4h&limit=21&symbol=BTCUSDT
2025-08-18 08:32:22,635 - services.market.market_service - ERROR - Error fetching Fear & Greed Index: Connection timeout to host https://api.alternative.me/fng/
2025-08-18 11:03:43,022 - discord.client - ERROR - Attempting a reconnect in 0.90s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-18 12:35:40,441 - discord.client - ERROR - Attempting a reconnect in 0.17s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-18 15:09:44,247 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 44.8s behind.
2025-08-18 15:25:03,395 - discord.client - ERROR - Attempting a reconnect in 1.31s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-18 19:02:18,981 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 484, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/trading/data_service.py", line 64, in _update_loop
    await self.update_all_data()
  File "/root/chartfix/services/trading/data_service.py", line 80, in update_all_data
    account_result = self.trading_service.get_account_balance()
  File "/root/chartfix/services/core/error_service.py", line 83, in wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/core/error_service.py", line 171, in sync_wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/trading/trading_service.py", line 67, in get_account_balance
    account_info = self.exchange.fapiPrivateV3GetAccount()
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/adapters.py", line 486, in send
    resp = conn.urlopen(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 700, in urlopen
    httplib_response = self._make_request(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 383, in _make_request
    self._validate_conn(conn)
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 1017, in _validate_conn
    conn.connect()
  File "/usr/lib/python3/dist-packages/urllib3/connection.py", line 353, in connect
    conn = self._new_conn()
  File "/usr/lib/python3/dist-packages/urllib3/connection.py", line 169, in _new_conn
    conn = connection.create_connection(
  File "/usr/lib/python3/dist-packages/urllib3/util/connection.py", line 86, in create_connection
    sock.connect(sa)

2025-08-18 19:02:24,731 - services.trading.trading_service - ERROR - Network error fetching balance: binance {"code":-1021,"msg":"Timestamp for this request is outside of the recvWindow."}
2025-08-18 19:02:24,731 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance {"code":-1021,"msg":"Timestamp for this request is outside of the recvWindow."} (Caused by: InvalidNonce: binance {"code":-1021,"msg":"Timestamp for this request is outside of the recvWindow."})
2025-08-18 19:02:28,983 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 484, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/trading/data_service.py", line 64, in _update_loop
    await self.update_all_data()
  File "/root/chartfix/services/trading/data_service.py", line 128, in update_all_data
    orders_result = self.trading_service.get_open_orders()
  File "/root/chartfix/services/core/error_service.py", line 83, in wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/trading/trading_service.py", line 335, in get_open_orders
    orders = self.exchange.fetch_open_orders(formatted_symbol)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 6739, in fetch_open_orders
    response = self.fapiPrivateGetOpenOrders(self.extend(request, params))
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/adapters.py", line 486, in send
    resp = conn.urlopen(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 700, in urlopen
    httplib_response = self._make_request(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 441, in _make_request
    httplib_response = conn.getresponse()
  File "/usr/lib/python3.10/http/client.py", line 1375, in getresponse
    response.begin()
  File "/usr/lib/python3.10/http/client.py", line 318, in begin
    version, status, reason = self._read_status()
  File "/usr/lib/python3.10/http/client.py", line 279, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
  File "/usr/lib/python3.10/socket.py", line 705, in readinto
    return self._sock.recv_into(b)
  File "/usr/lib/python3.10/ssl.py", line 1303, in recv_into
    return self.read(nbytes, buffer)
  File "/usr/lib/python3.10/ssl.py", line 1159, in read
    return self._sslobj.read(len, buffer)

2025-08-18 19:02:29,026 - discord.client - ERROR - Attempting a reconnect in 1.85s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-26 11:24:58,210 - discord.client - WARNING - PyNaCl is not installed, voice will NOT be supported
2025-08-26 11:26:07,679 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x7e6cc84c6330>, 54202.705068094)])']
connector: <aiohttp.connector.TCPConnector object at 0x7e6ce0162e70>
2025-08-26 11:26:53,113 - discord.client - WARNING - PyNaCl is not installed, voice will NOT be supported
2025-08-26 11:27:45,710 - discord.client - WARNING - PyNaCl is not installed, voice will NOT be supported
2025-08-26 11:27:59,997 - discord.client - WARNING - PyNaCl is not installed, voice will NOT be supported
2025-08-26 11:28:20,789 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x79dc40b34e90>, 54334.216586865)])', 'deque([(<aiohttp.client_proto.ResponseHandler object at 0x79dc40b35130>, 54334.439321535)])']
connector: <aiohttp.connector.TCPConnector object at 0x79dc6b587c50>
2025-08-26 11:30:55,746 - discord.client - WARNING - PyNaCl is not installed, voice will NOT be supported
2025-08-26 11:33:33,729 - discord.client - WARNING - PyNaCl is not installed, voice will NOT be supported
2025-08-26 11:34:40,676 - discord.client - WARNING - PyNaCl is not installed, voice will NOT be supported
2025-08-26 11:36:24,520 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x7e1a55249490>, 54819.802550308)])']
connector: <aiohttp.connector.TCPConnector object at 0x7e1a82802210>
